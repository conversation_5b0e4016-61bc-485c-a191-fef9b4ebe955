<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI组件样式测试</title>
    <style>
        /* AI识别区域样式 - 完全按照原型设计 */
        .ai-section {
          width: 100% !important;
          height: 100% !important;
          background: white !important;
          padding: 20px !important;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
          display: flex !important;
          flex-direction: column !important;
          font-family: "Microsoft YaHei", Arial, sans-serif !important;
        }

        /* AI识别标题 */
        .ai-header {
          text-align: center !important;
          font-size: 24px !important;
          font-weight: bold !important;
          color: #1976D2 !important;
          margin-bottom: 20px !important;
          border-bottom: 2px solid #1976D2 !important;
          padding-bottom: 10px !important;
        }

        /* AI状态栏 */
        .ai-status-bar {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: #E3F2FD;
          padding: 12px 16px;
          border-radius: 8px;
          margin-bottom: 20px;
          font-size: 16px;
        }

        .status-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .status-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #4CAF50;
        }

        /* 摄像头预览区 */
        .camera-preview {
          width: 100% !important;
          height: 280px !important;
          background: linear-gradient(135deg, #BBDEFB, #90CAF9) !important;
          border: 3px solid #1976D2 !important;
          border-radius: 12px !important;
          display: flex !important;
          flex-direction: column !important;
          align-items: center !important;
          justify-content: center !important;
          color: #1976D2 !important;
          margin-bottom: 20px !important;
          position: relative !important;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
        }

        .camera-icon {
          font-size: 64px;
          margin-bottom: 15px;
        }

        .camera-status {
          font-size: 18px;
          text-align: center;
          font-weight: bold;
        }

        .camera-guide {
          position: absolute;
          bottom: 15px;
          left: 50%;
          transform: translateX(-50%);
          font-size: 14px;
          color: #1565C0;
          text-align: center;
        }

        /* 识别历史记录 */
        .recognition-history {
          background: #F5F5F5;
          border-radius: 12px;
          padding: 16px;
          margin-bottom: 20px;
          max-height: 200px;
          overflow-y: auto;
        }

        .history-header {
          font-size: 18px;
          font-weight: bold;
          color: #212121;
          margin-bottom: 12px;
          border-bottom: 1px solid #E0E0E0;
          padding-bottom: 8px;
        }

        .history-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .history-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 8px 12px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .history-icon {
          font-size: 24px;
        }

        .history-info {
          flex: 1;
        }

        .history-name {
          font-size: 16px;
          font-weight: bold;
          color: #212121;
        }

        .history-time {
          font-size: 12px;
          color: #757575;
        }

        .history-price {
          font-size: 14px;
          font-weight: bold;
          color: #4CAF50;
        }

        /* AI控制按钮 */
        .ai-controls {
          display: flex;
          gap: 12px;
          margin-top: auto;
        }

        .ai-btn {
          flex: 1;
          padding: 12px 16px;
          border: 2px solid #1976D2;
          background: white;
          color: #1976D2;
          border-radius: 8px;
          font-size: 16px;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.2s;
        }

        .ai-btn.success {
          background: #4CAF50;
          border-color: #4CAF50;
          color: white;
        }

        .ai-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        body {
          margin: 0;
          padding: 20px;
          background: #FAFAFA;
          font-family: "Microsoft YaHei", Arial, sans-serif;
        }

        .container {
          width: 400px;
          height: 600px;
          margin: 0 auto;
          background: white;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="ai-section">
            <!-- AI识别标题 -->
            <div class="ai-header">🤖 AI智能扫菜识别</div>
            
            <!-- AI状态栏 -->
            <div class="ai-status-bar">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>识别状态：已暂停</span>
                </div>
                <div class="status-indicator">
                    <span>🌡️ 光线：良好</span>
                </div>
            </div>

            <!-- 摄像头预览区 -->
            <div class="camera-preview">
                <div class="camera-icon">📸</div>
                <div class="camera-status">点击开始识别</div>
                <div class="camera-guide">💡 自动识别：检测到菜品将自动添加到订单</div>
            </div>

            <!-- 识别历史记录 -->
            <div class="recognition-history">
                <div class="history-header">📝 识别记录</div>
                <div class="history-list">
                    <div class="history-item">
                        <span class="history-icon">🍗</span>
                        <div class="history-info">
                            <div class="history-name">宫保鸡丁</div>
                            <div class="history-time">14:28 自动添加</div>
                        </div>
                        <div class="history-price">¥18.00</div>
                    </div>
                </div>
            </div>

            <!-- AI控制按钮 -->
            <div class="ai-controls">
                <button class="ai-btn">▶️ 开始识别</button>
                <button class="ai-btn">🗑️ 清空记录</button>
                <button class="ai-btn">⚙️ 设置</button>
            </div>
        </div>
    </div>
</body>
</html>
