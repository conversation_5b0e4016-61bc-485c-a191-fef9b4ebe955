import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from 'antd';

import CashierPage from './pages/CashierPage';
import './App.css';

const { Content } = Layout;

const App: React.FC = () => {
  return (
    <div className="App">
      <Router>
        <Layout style={{ minHeight: '100vh' }}>
          <Content>
            <Routes>
              <Route path="/" element={<CashierPage />} />
              <Route path="/cashier" element={<CashierPage />} />
            </Routes>
          </Content>
        </Layout>
      </Router>
    </div>
  );
};

export default App; 