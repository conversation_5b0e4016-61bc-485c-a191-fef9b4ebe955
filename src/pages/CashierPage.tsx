import React, { useEffect } from 'react';
import { Layout } from 'antd';
import { useAppDispatch } from '../hooks/redux';
import { fetchCategories } from '../store/slices/productSlice';

// 导入主要组件
import StatusBar from '../components/layout/StatusBar';
import OrderSection from '../components/sections/OrderSection';
import AISection from '../components/sections/AISection';
import MenuSection from '../components/sections/MenuSection';

import './CashierPage.less';

const { Content } = Layout;

/**
 * 收银页面 - 主界面
 * 
 * 布局结构：
 * - 顶部：系统状态栏 (60px)
 * - 主体：三栏布局
 *   - 左侧30%：订单清单区域
 *   - 中间40%：AI菜品识别区域  
 *   - 右侧30%：菜品展示区域
 */
const CashierPage: React.FC = () => {
  const dispatch = useAppDispatch();

  // 页面初始化
  useEffect(() => {
    // 加载商品分类
    dispatch(fetchCategories());
  }, [dispatch]);

  return (
    <Layout className="cashier-page" style={{ height: '100vh' }}>
      {/* 系统状态栏 */}
      <StatusBar />
      
      {/* 主体内容 */}
      <Content style={{
        height: 'calc(100vh - 60px)',
        overflow: 'hidden',
        padding: 0,
        backgroundColor: '#FAFAFA'
      }}>
        <div style={{ 
          height: '100%', 
          display: 'flex', 
          gap: '2px',
          backgroundColor: '#FAFAFA'
        }}>
          {/* 左侧：订单清单区域 (30%) */}
          <div style={{ 
            width: '30%', 
            backgroundColor: 'white',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            overflow: 'auto',
            padding: '16px'
          }}>
            <div style={{ color: '#1976D2', fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
              📋 订单清单区域
            </div>
            <OrderSection />
          </div>
          
          {/* 中间：AI识别区域 (40%) */}
          <div style={{ 
            width: '40%', 
            backgroundColor: 'white',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            overflow: 'auto',
            padding: '16px'
          }}>
            <div style={{ color: '#1976D2', fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
              🤖 AI识别区域
            </div>
            <AISection />
          </div>
          
          {/* 右侧：菜品展示区域 (30%) */}
          <div style={{ 
            width: '30%', 
            backgroundColor: 'white',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            overflow: 'auto',
            padding: '16px'
          }}>
            <div style={{ color: '#1976D2', fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
              🍽️ 菜品展示区域
            </div>
            <MenuSection />
          </div>
        </div>
      </Content>
    </Layout>
  );
};

export default CashierPage; 