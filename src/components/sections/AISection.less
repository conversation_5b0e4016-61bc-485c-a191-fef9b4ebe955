// AI识别区域样式 - 增强版
.ai-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  padding: 20px;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);

  // AI标题和控制区域
  .ai-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #1976D2, #1565C0);
    border-radius: 12px;
    color: white;
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);

    .ai-title {
      display: flex;
      align-items: center;
      gap: 8px;

      h3 {
        margin: 0;
        color: white;
        font-size: 20px;
        font-weight: bold;
      }
    }

    .ai-controls-header {
      .elder-text {
        color: white;
        font-weight: 500;
      }
    }
  }

  // AI状态栏
  .ai-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #E3F2FD;
    border-radius: 8px;
    border-left: 4px solid #1976D2;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #bdbdbd;
        transition: all 0.3s ease;

        &.active {
          background: #4CAF50;
          animation: pulse 2s infinite;
        }
      }
    }

    .status-info {
      .elder-text {
        color: #1565C0;
      }
    }
  }

  // 摄像头预览区域 - 增强版
  .camera-preview {
    width: 100%;
    height: 280px;
    background: linear-gradient(135deg, #BBDEFB, #90CAF9);
    border: 3px solid #1976D2;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #1976D2;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;

    &.scanning {
      border-color: #4CAF50;
      background: linear-gradient(135deg, #C8E6C9, #A5D6A7);
      animation: scanning-pulse 2s infinite;
    }

    .video-container {
      width: 100%;
      height: 100%;
      position: relative;

      .camera-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
      }

      .detection-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;

        .detection-box {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 200px;
          height: 150px;
          border: 3px solid #4CAF50;
          border-radius: 8px;
          background: rgba(76, 175, 80, 0.1);
          animation: detection-highlight 1s ease-in-out;

          .detection-label {
            position: absolute;
            top: -40px;
            left: 0;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            white-space: nowrap;

            .dish-name {
              margin-right: 8px;
            }

            .confidence {
              background: rgba(255, 255, 255, 0.2);
              padding: 2px 6px;
              border-radius: 3px;
              font-size: 12px;
            }
          }
        }
      }
    }

    .camera-placeholder {
      text-align: center;

      .camera-icon {
        font-size: 64px;
        margin-bottom: 15px;
      }

      .camera-status {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .camera-guide {
        font-size: 14px;
        color: #1565C0;
        text-align: center;
        max-width: 280px;
      }
    }
  }

  // AI控制按钮区域
  .ai-controls {
    margin: 16px 0;

    .control-btn {
      height: 48px;
      font-size: 16px;
      font-weight: bold;
      border-radius: 8px;
      transition: all 0.3s ease;

      &.start-btn {
        background: linear-gradient(135deg, #4CAF50, #388E3C);
        border-color: #4CAF50;
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
        }
      }

      &.pause-btn {
        background: linear-gradient(135deg, #FF9800, #F57C00);
        border-color: #FF9800;
        box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(255, 152, 0, 0.4);
        }
      }
    }
  }

  // 识别历史记录区域
  .recognition-history {
    flex: 1;
    background: #F5F5F5;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    max-height: 300px;
    overflow-y: auto;

    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #E0E0E0;
    }

    .history-list {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .history-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        animation: slideIn 0.3s ease-out;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        &.added {
          border-left: 4px solid #4CAF50;
          background: #F1F8E9;
        }

        .history-icon {
          font-size: 24px;
          flex-shrink: 0;
        }

        .history-info {
          flex: 1;

          .history-name {
            margin-bottom: 4px;
          }

          .history-time {
            opacity: 0.7;
          }
        }

        .history-price {
          font-weight: bold;
          flex-shrink: 0;
        }

        .added-mark {
          flex-shrink: 0;
          margin-left: 8px;
        }
      }
    }

    .empty-history {
      text-align: center;
      padding: 40px 20px;
      color: #bdbdbd;

      p {
        margin-top: 16px;
      }
    }
  }

  // 底部控制区域
  .ai-bottom-controls {
    margin-top: auto;
    padding: 12px;
    background: #F5F5F5;
    border-radius: 8px;

    .ant-btn {
      height: 36px;
      font-size: 14px;
      border-radius: 6px;
    }
  }

  // 当前识别结果
  .current-result {
    flex: 0 0 auto;
    border: 2px solid #1677ff;
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.15);
    
    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
      
      .ant-typography {
        margin: 0;
        color: #1677ff;
        font-weight: 600;
      }
      
      .confidence-display {
        .elder-text {
          color: #595959;
          font-weight: 500;
        }
      }
    }
    
    .result-content {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      
      .dish-info {
        flex: 1;
        
        h3 {
          margin: 0 0 8px 0;
          color: #262626;
        }
        
        p {
          margin: 4px 0;
          line-height: 1.4;
          
          &:last-child {
            margin-top: 8px;
            font-size: 18px;
            font-weight: 600;
            color: #f5222d;
          }
        }
      }
      
      .dish-image {
        flex: 0 0 80px;
        
        img {
          width: 80px;
          height: 80px;
          border-radius: 8px;
          object-fit: cover;
          border: 1px solid #f0f0f0;
        }
      }
    }
    
    .result-actions {
      .auto-add-notice {
        border-radius: 6px;
        
        .ant-alert-content {
          .ant-alert-message {
            font-size: 14px;
            font-weight: 500;
          }
        }
        
        .ant-alert-action {
          .ant-btn {
            height: 24px;
            padding: 0 8px;
            font-size: 12px;
          }
        }
      }
      
      .ant-space {
        width: 100%;
        
        .ant-btn {
          min-height: 48px;
          font-size: 16px;
          font-weight: 500;
          border-radius: 6px;
          
          &:first-child {
            border-color: #d9d9d9;
            color: #595959;
            
            &:hover {
              border-color: #40a9ff;
              color: #40a9ff;
            }
          }
          
          &:last-child {
            background: linear-gradient(135deg, #1677ff 0%, #40a9ff 100%);
            border-color: #1677ff;
            box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);
            
            &:hover {
              background: linear-gradient(135deg, #0958d9 0%, #1677ff 100%);
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(22, 119, 255, 0.4);
            }
          }
        }
      }
    }
  }

  // AI识别历史
  .ai-history {
    flex: 1 1 auto;
    min-height: 200px;
  }

  // AI状态信息
  .ai-status-info {
    flex: 0 0 auto;
    padding: 12px;
    background-color: #f5f5f5;
    border-radius: 6px;
    text-align: center;
    
    .ant-space {
      .elder-text {
        color: #8c8c8c;
        font-size: 12px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .ai-section {
    gap: 14px;
    padding: 14px;
    
    .model-loading-alert {
      margin-bottom: 14px;
      
      .ant-alert-content {
        .ant-alert-message {
          font-size: 15px;
        }
        
        .ant-alert-description {
          font-size: 13px;
        }
      }
    }
    
    .camera-section {
      .section-header {
        margin-bottom: 14px;
        padding-bottom: 10px;
        
        .ant-typography {
          font-size: 16px;
        }
        
        .ant-btn {
          height: 28px;
          padding: 0 10px;
          font-size: 13px;
        }
      }
    }
    
    .current-result {
      .result-header {
        margin-bottom: 14px;
        padding-bottom: 10px;
        
        .ant-typography {
          font-size: 15px;
        }
        
        .confidence-display {
          .elder-text {
            font-size: 13px;
          }
        }
      }
      
      .result-content {
        gap: 14px;
        margin-bottom: 14px;
        
        .dish-info {
          h3 {
            font-size: 16px;
            margin-bottom: 6px;
          }
          
          p {
            font-size: 13px;
            
            &:last-child {
              font-size: 16px;
            }
          }
        }
        
        .dish-image {
          flex: 0 0 70px;
          
          img {
            width: 70px;
            height: 70px;
          }
        }
      }
      
      .result-actions {
        .ant-space {
          .ant-btn {
            min-height: 44px;
            font-size: 15px;
          }
        }
      }
    }
    
    .ai-status-info {
      padding: 10px;
      
      .ant-space {
        .elder-text {
          font-size: 11px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .ai-section {
    gap: 12px;
    padding: 12px;
    
    .model-loading-alert {
      margin-bottom: 12px;
      
      .ant-alert-content {
        .ant-alert-message {
          font-size: 14px;
        }
        
        .ant-alert-description {
          font-size: 12px;
        }
      }
    }
    
    .camera-section {
      .section-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
        
        .ant-typography {
          font-size: 15px;
        }
        
        .ant-btn {
          height: 26px;
          padding: 0 8px;
          font-size: 12px;
        }
      }
    }
    
    .current-result {
      .result-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
        
        .ant-typography {
          font-size: 14px;
        }
        
        .confidence-display {
          .elder-text {
            font-size: 12px;
          }
        }
      }
      
      .result-content {
        flex-direction: column;
        gap: 12px;
        margin-bottom: 12px;
        
        .dish-info {
          h3 {
            font-size: 15px;
            margin-bottom: 4px;
          }
          
          p {
            font-size: 12px;
            
            &:last-child {
              font-size: 15px;
            }
          }
        }
        
        .dish-image {
          flex: 0 0 auto;
          align-self: center;
          
          img {
            width: 60px;
            height: 60px;
          }
        }
      }
      
      .result-actions {
        .auto-add-notice {
          .ant-alert-content {
            .ant-alert-message {
              font-size: 13px;
            }
          }
          
          .ant-alert-action {
            .ant-btn {
              height: 22px;
              padding: 0 6px;
              font-size: 11px;
            }
          }
        }
        
        .ant-space {
          .ant-btn {
            min-height: 40px;
            font-size: 14px;
          }
        }
      }
    }
    
    .ai-status-info {
      padding: 8px;
      
      .ant-space {
        flex-wrap: wrap;
        justify-content: center;
        
        .elder-text {
          font-size: 10px;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .ai-section {
    background-color: #ffffff;
    
    .model-loading-alert {
      border: 2px solid #1677ff;
    }
    
    .camera-section {
      .section-header {
        border-bottom: 2px solid #000000;
        
        .ant-typography {
          color: #000000;
        }
      }
    }
    
    .current-result {
      border: 3px solid #0050b3;
      
      .result-header {
        border-bottom: 2px solid #000000;
        
        .ant-typography {
          color: #0050b3;
        }
        
        .confidence-display {
          .elder-text {
            color: #434343;
          }
        }
      }
      
      .result-content {
        .dish-info {
          h3 {
            color: #000000;
          }
          
          p {
            color: #434343;
            
            &:last-child {
              color: #d4380d;
            }
          }
        }
        
        .dish-image {
          img {
            border: 2px solid #000000;
          }
        }
      }
      
      .result-actions {
        .ant-space {
          .ant-btn {
            border: 2px solid #000000;
            
            &:first-child {
              background-color: #ffffff;
              color: #000000;
              
              &:hover {
                background-color: #f0f0f0;
              }
            }
            
            &:last-child {
              background-color: #0050b3;
              color: #ffffff;
              
              &:hover {
                background-color: #003a8c;
              }
            }
          }
        }
      }
    }
    
    .ai-status-info {
      background-color: #f5f5f5;
      border: 1px solid #434343;
      
      .ant-space {
        .elder-text {
          color: #434343;
        }
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes scanning-pulse {
  0% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
  50% { box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3); }
  100% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
}

@keyframes detection-highlight {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
  100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// 打印样式
@media print {
  .ai-section {
    .ai-header {
      background: white !important;
      color: black !important;
      box-shadow: none !important;
    }

    .ai-controls {
      display: none;
    }

    .ai-bottom-controls {
      display: none;
    }

    .camera-preview {
      border: 1px solid #000000;
      box-shadow: none;
    }
  }
}