// AI识别区域样式 - 原型风格
.ai-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: white;

  // AI识别标题
  .ai-header {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    color: #1976D2;
    margin-bottom: 20px;
    border-bottom: 2px solid #1976D2;
    padding-bottom: 10px;
  }

  // AI状态栏
  .ai-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #E3F2FD;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 16px;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #4CAF50;

      &.active {
        animation: pulse 2s infinite;
      }
    }
  }

  // 摄像头预览区域
  .camera-preview {
    width: 100%;
    height: 280px;
    background: linear-gradient(135deg, #BBDEFB, #90CAF9);
    border: 3px solid #1976D2;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #1976D2;
    margin-bottom: 20px;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    &.scanning {
      animation: pulse 1.5s infinite;
    }

    .camera-icon {
      font-size: 64px;
      margin-bottom: 15px;
    }

    .camera-status {
      font-size: 18px;
      text-align: center;
      font-weight: bold;
    }

    .camera-guide {
      position: absolute;
      bottom: 15px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 14px;
      color: #1565C0;
      text-align: center;
    }
  }

  // 识别历史记录样式
  .recognition-history {
    background: #F5F5F5;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    max-height: 200px;
    overflow-y: auto;

    .history-header {
      font-size: 18px;
      font-weight: bold;
      color: #212121;
      margin-bottom: 12px;
      border-bottom: 1px solid #E0E0E0;
      padding-bottom: 8px;
    }

    .history-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .history-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 12px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      animation: slideIn 0.3s ease-out;

      .history-icon {
        font-size: 24px;
      }

      .history-info {
        flex: 1;

        .history-name {
          font-size: 16px;
          font-weight: bold;
          color: #212121;
        }

        .history-time {
          font-size: 12px;
          color: #757575;
        }
      }

      .history-price {
        font-size: 14px;
        font-weight: bold;
        color: #4CAF50;
      }
    }
  }

  // AI控制按钮
  .ai-controls {
    display: flex;
    gap: 12px;
    margin-top: auto;

    .ai-btn {
      flex: 1;
      padding: 12px 16px;
      border: 2px solid #1976D2;
      background: white;
      color: #1976D2;
      border-radius: 8px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.2s;

      &.success {
        background: #4CAF50;
        border-color: #4CAF50;
        color: white;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
    }
  }

  // 自动识别状态样式
  &.auto-mode .camera-preview {
    border-color: #4CAF50;
    background: linear-gradient(135deg, #C8E6C9, #A5D6A7);
  }

  &.auto-mode .status-dot {
    background: #4CAF50;
    animation: pulse 2s infinite;
  }
}

// 动画效果
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
