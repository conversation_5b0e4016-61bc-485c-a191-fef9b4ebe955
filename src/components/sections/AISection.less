// AI识别区域样式
.ai-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  padding: 16px;
  background-color: #fafafa;

  // AI模型加载提示
  .model-loading-alert {
    margin-bottom: 16px;
    border-radius: 8px;
    
    .ant-alert-content {
      .ant-alert-message {
        font-size: 16px;
        font-weight: 600;
      }
      
      .ant-alert-description {
        font-size: 14px;
        margin-top: 4px;
      }
    }
  }

  // 摄像头预览区域
  .camera-section {
    flex: 0 0 auto;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
      
      .ant-typography {
        margin: 0;
        color: #262626;
      }
      
      .ant-btn {
        height: 32px;
        padding: 0 12px;
        font-size: 14px;
      }
    }
  }

  // 当前识别结果
  .current-result {
    flex: 0 0 auto;
    border: 2px solid #1677ff;
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.15);
    
    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
      
      .ant-typography {
        margin: 0;
        color: #1677ff;
        font-weight: 600;
      }
      
      .confidence-display {
        .elder-text {
          color: #595959;
          font-weight: 500;
        }
      }
    }
    
    .result-content {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      
      .dish-info {
        flex: 1;
        
        h3 {
          margin: 0 0 8px 0;
          color: #262626;
        }
        
        p {
          margin: 4px 0;
          line-height: 1.4;
          
          &:last-child {
            margin-top: 8px;
            font-size: 18px;
            font-weight: 600;
            color: #f5222d;
          }
        }
      }
      
      .dish-image {
        flex: 0 0 80px;
        
        img {
          width: 80px;
          height: 80px;
          border-radius: 8px;
          object-fit: cover;
          border: 1px solid #f0f0f0;
        }
      }
    }
    
    .result-actions {
      .auto-add-notice {
        border-radius: 6px;
        
        .ant-alert-content {
          .ant-alert-message {
            font-size: 14px;
            font-weight: 500;
          }
        }
        
        .ant-alert-action {
          .ant-btn {
            height: 24px;
            padding: 0 8px;
            font-size: 12px;
          }
        }
      }
      
      .ant-space {
        width: 100%;
        
        .ant-btn {
          min-height: 48px;
          font-size: 16px;
          font-weight: 500;
          border-radius: 6px;
          
          &:first-child {
            border-color: #d9d9d9;
            color: #595959;
            
            &:hover {
              border-color: #40a9ff;
              color: #40a9ff;
            }
          }
          
          &:last-child {
            background: linear-gradient(135deg, #1677ff 0%, #40a9ff 100%);
            border-color: #1677ff;
            box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);
            
            &:hover {
              background: linear-gradient(135deg, #0958d9 0%, #1677ff 100%);
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(22, 119, 255, 0.4);
            }
          }
        }
      }
    }
  }

  // AI识别历史
  .ai-history {
    flex: 1 1 auto;
    min-height: 200px;
  }

  // AI状态信息
  .ai-status-info {
    flex: 0 0 auto;
    padding: 12px;
    background-color: #f5f5f5;
    border-radius: 6px;
    text-align: center;
    
    .ant-space {
      .elder-text {
        color: #8c8c8c;
        font-size: 12px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .ai-section {
    gap: 14px;
    padding: 14px;
    
    .model-loading-alert {
      margin-bottom: 14px;
      
      .ant-alert-content {
        .ant-alert-message {
          font-size: 15px;
        }
        
        .ant-alert-description {
          font-size: 13px;
        }
      }
    }
    
    .camera-section {
      .section-header {
        margin-bottom: 14px;
        padding-bottom: 10px;
        
        .ant-typography {
          font-size: 16px;
        }
        
        .ant-btn {
          height: 28px;
          padding: 0 10px;
          font-size: 13px;
        }
      }
    }
    
    .current-result {
      .result-header {
        margin-bottom: 14px;
        padding-bottom: 10px;
        
        .ant-typography {
          font-size: 15px;
        }
        
        .confidence-display {
          .elder-text {
            font-size: 13px;
          }
        }
      }
      
      .result-content {
        gap: 14px;
        margin-bottom: 14px;
        
        .dish-info {
          h3 {
            font-size: 16px;
            margin-bottom: 6px;
          }
          
          p {
            font-size: 13px;
            
            &:last-child {
              font-size: 16px;
            }
          }
        }
        
        .dish-image {
          flex: 0 0 70px;
          
          img {
            width: 70px;
            height: 70px;
          }
        }
      }
      
      .result-actions {
        .ant-space {
          .ant-btn {
            min-height: 44px;
            font-size: 15px;
          }
        }
      }
    }
    
    .ai-status-info {
      padding: 10px;
      
      .ant-space {
        .elder-text {
          font-size: 11px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .ai-section {
    gap: 12px;
    padding: 12px;
    
    .model-loading-alert {
      margin-bottom: 12px;
      
      .ant-alert-content {
        .ant-alert-message {
          font-size: 14px;
        }
        
        .ant-alert-description {
          font-size: 12px;
        }
      }
    }
    
    .camera-section {
      .section-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
        
        .ant-typography {
          font-size: 15px;
        }
        
        .ant-btn {
          height: 26px;
          padding: 0 8px;
          font-size: 12px;
        }
      }
    }
    
    .current-result {
      .result-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
        
        .ant-typography {
          font-size: 14px;
        }
        
        .confidence-display {
          .elder-text {
            font-size: 12px;
          }
        }
      }
      
      .result-content {
        flex-direction: column;
        gap: 12px;
        margin-bottom: 12px;
        
        .dish-info {
          h3 {
            font-size: 15px;
            margin-bottom: 4px;
          }
          
          p {
            font-size: 12px;
            
            &:last-child {
              font-size: 15px;
            }
          }
        }
        
        .dish-image {
          flex: 0 0 auto;
          align-self: center;
          
          img {
            width: 60px;
            height: 60px;
          }
        }
      }
      
      .result-actions {
        .auto-add-notice {
          .ant-alert-content {
            .ant-alert-message {
              font-size: 13px;
            }
          }
          
          .ant-alert-action {
            .ant-btn {
              height: 22px;
              padding: 0 6px;
              font-size: 11px;
            }
          }
        }
        
        .ant-space {
          .ant-btn {
            min-height: 40px;
            font-size: 14px;
          }
        }
      }
    }
    
    .ai-status-info {
      padding: 8px;
      
      .ant-space {
        flex-wrap: wrap;
        justify-content: center;
        
        .elder-text {
          font-size: 10px;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .ai-section {
    background-color: #ffffff;
    
    .model-loading-alert {
      border: 2px solid #1677ff;
    }
    
    .camera-section {
      .section-header {
        border-bottom: 2px solid #000000;
        
        .ant-typography {
          color: #000000;
        }
      }
    }
    
    .current-result {
      border: 3px solid #0050b3;
      
      .result-header {
        border-bottom: 2px solid #000000;
        
        .ant-typography {
          color: #0050b3;
        }
        
        .confidence-display {
          .elder-text {
            color: #434343;
          }
        }
      }
      
      .result-content {
        .dish-info {
          h3 {
            color: #000000;
          }
          
          p {
            color: #434343;
            
            &:last-child {
              color: #d4380d;
            }
          }
        }
        
        .dish-image {
          img {
            border: 2px solid #000000;
          }
        }
      }
      
      .result-actions {
        .ant-space {
          .ant-btn {
            border: 2px solid #000000;
            
            &:first-child {
              background-color: #ffffff;
              color: #000000;
              
              &:hover {
                background-color: #f0f0f0;
              }
            }
            
            &:last-child {
              background-color: #0050b3;
              color: #ffffff;
              
              &:hover {
                background-color: #003a8c;
              }
            }
          }
        }
      }
    }
    
    .ai-status-info {
      background-color: #f5f5f5;
      border: 1px solid #434343;
      
      .ant-space {
        .elder-text {
          color: #434343;
        }
      }
    }
  }
}

// 打印样式
@media print {
  .ai-section {
    .camera-section {
      .section-header {
        .ant-btn {
          display: none;
        }
      }
    }
    
    .current-result {
      border: 1px solid #000000;
      box-shadow: none;
      
      .result-actions {
        display: none;
      }
    }
    
    .ai-status-info {
      display: none;
    }
  }
} 