import React, { useState, useEffect, useRef } from 'react';
import { Card, Badge, List, Progress, Button, Space, Avatar, Switch } from 'antd';
import { 
  CameraOutlined, 
  EyeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { addToCart } from '../../store/slices/orderSlice';
import { ElderCard, ElderButton } from '../elder';
import './AISection.less';

export interface AISectionProps {
  className?: string;
}

// 模拟AI识别结果
interface RecognitionResult {
  id: string;
  dishName: string;
  confidence: number;
  price: number;
  timestamp: string;
  image?: string;
  added: boolean;
}

/**
 * AI识别区域组件
 * 
 * 功能特性：
 * - 实时摄像头画面显示
 * - AI菜品识别结果展示
 * - 识别历史记录
 * - 自动添加到购物车
 * - 识别参数设置
 */
const AISection: React.FC<AISectionProps> = ({
  className = '',
}) => {
  const dispatch = useAppDispatch();
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // 状态管理
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [recognitionResults, setRecognitionResults] = useState<RecognitionResult[]>([]);
  const [currentResult, setCurrentResult] = useState<RecognitionResult | null>(null);
  const [autoAdd, setAutoAdd] = useState(true);
  const [cameraEnabled, setCameraEnabled] = useState(false);

  // 模拟识别配置
  const [recognitionConfig] = useState({
    confidence: 0.85,
    interval: 3000,
    enabled: true
  });

  // 启动摄像头
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraEnabled(true);
      }
    } catch (error) {
      console.error('启动摄像头失败:', error);
    }
  };

  // 停止摄像头
  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setCameraEnabled(false);
    }
  };

  // 模拟AI识别
  const simulateRecognition = () => {
    const mockDishes = [
      { name: '宫保鸡丁', price: 28.0 },
      { name: '红烧肉', price: 35.0 },
      { name: '麻婆豆腐', price: 18.0 },
      { name: '鱼香肉丝', price: 25.0 },
      { name: '番茄鸡蛋汤', price: 16.0 }
    ];
    
    const randomDish = mockDishes[Math.floor(Math.random() * mockDishes.length)];
    const confidence = 0.7 + Math.random() * 0.3; // 70%-100%
    
    const result: RecognitionResult = {
      id: `ai_${Date.now()}`,
      dishName: randomDish.name,
      confidence,
      price: randomDish.price,
      timestamp: new Date().toLocaleTimeString(),
      added: false
    };

    setCurrentResult(result);
    setRecognitionResults(prev => [result, ...prev.slice(0, 9)]); // 保留最近10条

    // 如果置信度够高且启用自动添加
    if (confidence >= recognitionConfig.confidence && autoAdd) {
      handleAddToCart(result);
    }
  };

  // 添加到购物车
  const handleAddToCart = (result: RecognitionResult) => {
    dispatch(addToCart({
      id: result.id,
      productId: result.id,
      name: result.dishName,
      price: result.price,
      quantity: 1,
      unit: '份',
      categoryId: 'ai_recognized',
      categoryName: 'AI识别',
    }));

    // 更新结果状态
    setRecognitionResults(prev => 
      prev.map(item => 
        item.id === result.id ? { ...item, added: true } : item
      )
    );

    if (currentResult?.id === result.id) {
      setCurrentResult({ ...result, added: true });
    }
  };

  // 开始/停止识别
  const toggleRecognition = () => {
    if (isRecognizing) {
      setIsRecognizing(false);
    } else {
      if (!cameraEnabled) {
        startCamera();
      }
      setIsRecognizing(true);
    }
  };

  // 识别循环
  useEffect(() => {
    let interval: any;
    
    if (isRecognizing && cameraEnabled && recognitionConfig.enabled) {
      interval = setInterval(simulateRecognition, recognitionConfig.interval);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecognizing, cameraEnabled, recognitionConfig]);

  // 渲染识别结果项
  const renderResultItem = (result: RecognitionResult) => (
    <List.Item 
      key={result.id}
      className={`recognition-item ${result.added ? 'added' : ''}`}
    >
      <div className="result-content">
        <div className="result-header">
          <span className="dish-name elder-text elder-text-base elder-text-bold">
            {result.dishName}
          </span>
          <Badge 
            status={result.confidence >= recognitionConfig.confidence ? 'success' : 'warning'}
            text={`${Math.round(result.confidence * 100)}%`}
            style={{ fontSize: '12px' }}
          />
        </div>
        <div className="result-footer">
          <span className="price elder-text elder-text-sm elder-text-error">
            ¥{result.price.toFixed(2)}
          </span>
          <span className="timestamp elder-text elder-text-xs elder-text-secondary">
            {result.timestamp}
          </span>
          {!result.added && result.confidence >= recognitionConfig.confidence && (
            <ElderButton
              type="primary"
              size="default"
              onClick={() => handleAddToCart(result)}
            >
              添加
            </ElderButton>
          )}
          {result.added && (
            <span className="added-mark elder-text elder-text-xs elder-text-success">
              <CheckCircleOutlined style={{ marginRight: 4 }} />
              已添加
            </span>
          )}
        </div>
      </div>
    </List.Item>
  );

  return (
    <div className={`ai-section ${className}`}>
      {/* AI识别标题 */}
      <div className="ai-header">
        <h3 className="elder-text elder-text-lg elder-text-bold">
          <EyeOutlined style={{ marginRight: 8 }} />
          AI智能识别
          {isRecognizing && (
            <Badge 
              status="processing" 
              text="识别中..." 
              style={{ marginLeft: 8 }}
            />
          )}
        </h3>
        <Space>
          <span className="elder-text elder-text-sm">自动添加</span>
          <Switch 
            checked={autoAdd} 
            onChange={setAutoAdd}
            size="default"
          />
        </Space>
      </div>

      {/* 摄像头画面区域 */}
      <Card className="camera-card" size="small">
        <div className="camera-container">
          {cameraEnabled ? (
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="camera-video"
            />
          ) : (
            <div className="camera-placeholder">
              <CameraOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
              <p className="elder-text elder-text-base elder-text-secondary">
                点击开启摄像头
              </p>
            </div>
          )}
          
          {/* 摄像头控制按钮 */}
          <div className="camera-controls">
            <ElderButton
              type={isRecognizing ? 'default' : 'primary'}
              size="large"
              icon={isRecognizing ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={toggleRecognition}
              block
            >
              {isRecognizing ? '停止识别' : '开始识别'}
            </ElderButton>
          </div>
        </div>
      </Card>

      {/* 当前识别结果 */}
      {currentResult && (
        <Card className="current-result-card" size="small">
          <div className="current-result">
            <div className="result-info">
              <div className="dish-name elder-text elder-text-lg elder-text-bold">
                {currentResult.dishName}
              </div>
              <div className="confidence-bar">
                <Progress 
                  percent={Math.round(currentResult.confidence * 100)}
                  size="small"
                  status={currentResult.confidence >= recognitionConfig.confidence ? 'success' : 'normal'}
                  showInfo={false}
                />
                <span className="confidence-text elder-text elder-text-sm">
                  置信度: {Math.round(currentResult.confidence * 100)}%
                </span>
              </div>
              <div className="price elder-text elder-text-base elder-text-error">
                ¥{currentResult.price.toFixed(2)}
              </div>
            </div>
            {!currentResult.added && currentResult.confidence >= recognitionConfig.confidence && (
              <ElderButton
                type="primary"
                size="large"
                onClick={() => handleAddToCart(currentResult)}
                icon={<CheckCircleOutlined />}
              >
                添加到订单
              </ElderButton>
            )}
          </div>
        </Card>
      )}

      {/* 识别历史 */}
      <Card 
        className="recognition-history-card" 
        size="small"
        title={
          <span className="elder-text elder-text-base elder-text-bold">
            <ClockCircleOutlined style={{ marginRight: 8 }} />
            识别历史
          </span>
        }
      >
        <div className="recognition-history">
          {recognitionResults.length > 0 ? (
            <List
              size="small"
              dataSource={recognitionResults}
              renderItem={renderResultItem}
              className="result-list"
            />
          ) : (
            <div className="empty-history">
              <EyeOutlined style={{ fontSize: 32, color: '#d9d9d9' }} />
              <p className="elder-text elder-text-sm elder-text-secondary">
                暂无识别记录
              </p>
            </div>
          )}
        </div>
      </Card>

      {/* 设置提示 */}
      <div className="ai-tips">
        <span className="elder-text elder-text-xs elder-text-secondary">
          <SettingOutlined style={{ marginRight: 4 }} />
          识别阈值: {Math.round(recognitionConfig.confidence * 100)}% | 
          扫描间隔: {recognitionConfig.interval / 1000}秒
        </span>
      </div>
    </div>
  );
};

export default AISection; 