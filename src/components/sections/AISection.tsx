import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Badge, Progress, Space, Switch, Tooltip } from 'antd';
import {
  EyeOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useAppDispatch } from '../../hooks/redux';
import { addToCart } from '../../store/slices/orderSlice';
import { ElderButton } from '../elder';
import './AISection.less';

export interface AISectionProps {
  className?: string;
}

// 模拟AI识别结果
interface RecognitionResult {
  id: string;
  dishName: string;
  confidence: number;
  price: number;
  timestamp: string;
  image?: string;
  added: boolean;
}

/**
 * AI识别区域组件
 * 
 * 功能特性：
 * - 实时摄像头画面显示
 * - AI菜品识别结果展示
 * - 识别历史记录
 * - 自动添加到购物车
 * - 识别参数设置
 */
const AISection: React.FC<AISectionProps> = ({
  className = '',
}) => {
  const dispatch = useAppDispatch();
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // 状态管理
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [recognitionResults, setRecognitionResults] = useState<RecognitionResult[]>([]);
  const [currentResult, setCurrentResult] = useState<RecognitionResult | null>(null);
  const [autoAdd, setAutoAdd] = useState(true);
  const [cameraEnabled, setCameraEnabled] = useState(false);

  // 模拟识别配置
  const [recognitionConfig] = useState({
    confidence: 0.85,
    interval: 3000,
    enabled: true
  });

  // 启动摄像头
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraEnabled(true);
      }
    } catch (error) {
      console.error('启动摄像头失败:', error);
    }
  };

  // 添加到购物车
  const handleAddToCart = useCallback((result: RecognitionResult) => {
    dispatch(addToCart({
      id: result.id,
      productId: result.id,
      name: result.dishName,
      price: result.price,
      quantity: 1,
      unit: '份',
      categoryId: 'ai_recognized',
      categoryName: 'AI识别',
    }));

    // 更新结果状态
    setRecognitionResults(prev =>
      prev.map(item =>
        item.id === result.id ? { ...item, added: true } : item
      )
    );

    if (currentResult?.id === result.id) {
      setCurrentResult({ ...result, added: true });
    }
  }, [dispatch, currentResult?.id]);

  // 模拟AI识别 - 增强版连续识别
  const simulateRecognition = useCallback(() => {
    const mockDishes = [
      { name: '宫保鸡丁', price: 28.0, icon: '🍗' },
      { name: '红烧肉', price: 35.0, icon: '🥩' },
      { name: '麻婆豆腐', price: 18.0, icon: '🍲' },
      { name: '鱼香肉丝', price: 25.0, icon: '🐟' },
      { name: '番茄鸡蛋汤', price: 16.0, icon: '🍜' },
      { name: '蒜蓉菜心', price: 12.0, icon: '🥬' },
      { name: '糖醋里脊', price: 22.0, icon: '🍖' },
      { name: '白米饭', price: 3.0, icon: '🍚' }
    ];

    const randomDish = mockDishes[Math.floor(Math.random() * mockDishes.length)];
    const confidence = 0.7 + Math.random() * 0.3; // 70%-100%

    const result: RecognitionResult = {
      id: `ai_${Date.now()}`,
      dishName: randomDish.name,
      confidence,
      price: randomDish.price,
      timestamp: new Date().toLocaleTimeString(),
      image: randomDish.icon,
      added: false
    };

    setCurrentResult(result);
    setRecognitionResults(prev => [result, ...prev.slice(0, 9)]); // 保留最近10条

    // 如果置信度够高且启用自动添加
    if (confidence >= recognitionConfig.confidence && autoAdd) {
      handleAddToCart(result);
    }
  }, [recognitionConfig.confidence, autoAdd, handleAddToCart]);

  // 开始/停止识别
  const toggleRecognition = () => {
    if (isRecognizing) {
      setIsRecognizing(false);
    } else {
      if (!cameraEnabled) {
        startCamera();
      }
      setIsRecognizing(true);
    }
  };

  // 识别循环
  useEffect(() => {
    let interval: any;

    if (isRecognizing && cameraEnabled && recognitionConfig.enabled) {
      interval = setInterval(simulateRecognition, recognitionConfig.interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecognizing, cameraEnabled, recognitionConfig, simulateRecognition]);

  return (
    <div className={`ai-section ${className}`}>
      {/* AI识别标题和状态栏 */}
      <div className="ai-header">
        <div className="ai-title">
          <h3 className="elder-text elder-text-lg elder-text-bold">
            🤖 AI智能扫菜识别
          </h3>
          {isRecognizing && (
            <Badge
              status="processing"
              text="连续识别中..."
              style={{ marginLeft: 8, fontSize: '14px' }}
            />
          )}
        </div>
        <div className="ai-controls-header">
          <Space size="middle">
            <Tooltip title="自动添加置信度高的识别结果">
              <span className="elder-text elder-text-sm">自动添加</span>
              <Switch
                checked={autoAdd}
                onChange={setAutoAdd}
                size="default"
                style={{ marginLeft: 8 }}
              />
            </Tooltip>
          </Space>
        </div>
      </div>

      {/* AI状态栏 */}
      <div className="ai-status-bar">
        <div className="status-indicator">
          <div className={`status-dot ${isRecognizing ? 'active' : ''}`}></div>
          <span className="elder-text elder-text-sm">
            识别状态：{isRecognizing ? '连续识别中' : '已暂停'}
          </span>
        </div>
        <div className="status-info">
          <span className="elder-text elder-text-xs elder-text-secondary">
            🌡️ 光线：良好 | 📊 置信度阈值：{Math.round(recognitionConfig.confidence * 100)}%
          </span>
        </div>
      </div>

      {/* 摄像头预览区域 - 增强版 */}
      <div className={`camera-preview ${isRecognizing ? 'scanning' : ''}`}>
        {cameraEnabled ? (
          <div className="video-container">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="camera-video"
            />
            {/* 识别框覆盖层 */}
            <div className="detection-overlay">
              {currentResult && (
                <div className="detection-box">
                  <div className="detection-label">
                    <span className="dish-name">{currentResult.dishName}</span>
                    <span className="confidence">{Math.round(currentResult.confidence * 100)}%</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="camera-placeholder">
            <div className="camera-icon">📸</div>
            <div className="camera-status">
              {isRecognizing ? '🔄 正在扫描...' : '点击开始识别'}
            </div>
            <div className="camera-guide">
              💡 自动识别：检测到菜品将自动添加到订单
            </div>
          </div>
        )}
      </div>

      {/* 摄像头控制按钮 */}
      <div className="ai-controls">
        <ElderButton
          type={isRecognizing ? 'default' : 'primary'}
          size="large"
          icon={isRecognizing ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          onClick={toggleRecognition}
          className={`control-btn ${isRecognizing ? 'pause-btn' : 'start-btn'}`}
          block
        >
          {isRecognizing ? '⏸️ 暂停识别' : '🔄 开始识别'}
        </ElderButton>
      </div>

      {/* 当前识别结果 */}
      {currentResult && (
        <Card className="current-result-card" size="small">
          <div className="current-result">
            <div className="result-info">
              <div className="dish-name elder-text elder-text-lg elder-text-bold">
                {currentResult.dishName}
              </div>
              <div className="confidence-bar">
                <Progress 
                  percent={Math.round(currentResult.confidence * 100)}
                  size="small"
                  status={currentResult.confidence >= recognitionConfig.confidence ? 'success' : 'normal'}
                  showInfo={false}
                />
                <span className="confidence-text elder-text elder-text-sm">
                  置信度: {Math.round(currentResult.confidence * 100)}%
                </span>
              </div>
              <div className="price elder-text elder-text-base elder-text-error">
                ¥{currentResult.price.toFixed(2)}
              </div>
            </div>
            {!currentResult.added && currentResult.confidence >= recognitionConfig.confidence && (
              <ElderButton
                type="primary"
                size="large"
                onClick={() => handleAddToCart(currentResult)}
                icon={<CheckCircleOutlined />}
              >
                添加到订单
              </ElderButton>
            )}
          </div>
        </Card>
      )}

      {/* 识别历史记录 - 增强版 */}
      <div className="recognition-history">
        <div className="history-header">
          <span className="elder-text elder-text-base elder-text-bold">
            📝 识别记录
          </span>
          {recognitionResults.length > 0 && (
            <span className="elder-text elder-text-xs elder-text-secondary">
              共 {recognitionResults.length} 条
            </span>
          )}
        </div>
        <div className="history-list">
          {recognitionResults.length > 0 ? (
            recognitionResults.map((result) => (
              <div key={result.id} className={`history-item ${result.added ? 'added' : ''}`}>
                <span className="history-icon">{result.image}</span>
                <div className="history-info">
                  <div className="history-name elder-text elder-text-sm elder-text-bold">
                    {result.dishName}
                  </div>
                  <div className="history-time elder-text elder-text-xs elder-text-secondary">
                    {result.timestamp} {result.added ? '自动添加' : '待确认'} ({Math.round(result.confidence * 100)}%)
                  </div>
                </div>
                <div className="history-price elder-text elder-text-sm elder-text-error">
                  ¥{result.price.toFixed(2)}
                </div>
                {result.added && (
                  <div className="added-mark">
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="empty-history">
              <EyeOutlined style={{ fontSize: 32, color: '#d9d9d9' }} />
              <p className="elder-text elder-text-sm elder-text-secondary">
                暂无识别记录
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 底部控制区域 */}
      <div className="ai-bottom-controls">
        <Space size="small" style={{ width: '100%', justifyContent: 'space-between' }}>
          <ElderButton
            size="default"
            icon={<SettingOutlined />}
            onClick={() => {/* 打开设置 */}}
          >
            ⚙️ 设置
          </ElderButton>
          <ElderButton
            size="default"
            onClick={() => setRecognitionResults([])}
            disabled={recognitionResults.length === 0}
          >
            🗑️ 清空记录
          </ElderButton>
        </Space>
      </div>
    </div>
  );
};

export default AISection; 