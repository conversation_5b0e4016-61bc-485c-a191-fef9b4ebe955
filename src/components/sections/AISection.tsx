import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useAppDispatch } from '../../hooks/redux';
import { addToCart } from '../../store/slices/orderSlice';
import './AISection.less';

export interface AISectionProps {
  className?: string;
}

// 模拟AI识别结果
interface RecognitionResult {
  id: string;
  dishName: string;
  confidence: number;
  price: number;
  timestamp: string;
  image?: string;
  added: boolean;
}

/**
 * AI识别区域组件
 * 
 * 功能特性：
 * - 实时摄像头画面显示
 * - AI菜品识别结果展示
 * - 识别历史记录
 * - 自动添加到购物车
 * - 识别参数设置
 */
const AISection: React.FC<AISectionProps> = ({
  className = '',
}) => {
  const dispatch = useAppDispatch();
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // 状态管理
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [recognitionResults, setRecognitionResults] = useState<RecognitionResult[]>([]);
  const [currentResult, setCurrentResult] = useState<RecognitionResult | null>(null);
  const [autoAdd, setAutoAdd] = useState(true);
  const [cameraEnabled, setCameraEnabled] = useState(false);

  // 模拟识别配置
  const [recognitionConfig] = useState({
    confidence: 0.85,
    interval: 3000,
    enabled: true
  });

  // 启动摄像头
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraEnabled(true);
      }
    } catch (error) {
      console.error('启动摄像头失败:', error);
    }
  };

  // 添加到购物车
  const handleAddToCart = useCallback((result: RecognitionResult) => {
    dispatch(addToCart({
      id: result.id,
      productId: result.id,
      name: result.dishName,
      price: result.price,
      quantity: 1,
      unit: '份',
      categoryId: 'ai_recognized',
      categoryName: 'AI识别',
    }));

    // 更新结果状态
    setRecognitionResults(prev =>
      prev.map(item =>
        item.id === result.id ? { ...item, added: true } : item
      )
    );

    if (currentResult?.id === result.id) {
      setCurrentResult({ ...result, added: true });
    }
  }, [dispatch, currentResult?.id]);

  // 模拟AI识别 - 增强版连续识别
  const simulateRecognition = useCallback(() => {
    const mockDishes = [
      { name: '宫保鸡丁', price: 28.0, icon: '🍗' },
      { name: '红烧肉', price: 35.0, icon: '🥩' },
      { name: '麻婆豆腐', price: 18.0, icon: '🍲' },
      { name: '鱼香肉丝', price: 25.0, icon: '🐟' },
      { name: '番茄鸡蛋汤', price: 16.0, icon: '🍜' },
      { name: '蒜蓉菜心', price: 12.0, icon: '🥬' },
      { name: '糖醋里脊', price: 22.0, icon: '🍖' },
      { name: '白米饭', price: 3.0, icon: '🍚' }
    ];

    const randomDish = mockDishes[Math.floor(Math.random() * mockDishes.length)];
    const confidence = 0.7 + Math.random() * 0.3; // 70%-100%

    const result: RecognitionResult = {
      id: `ai_${Date.now()}`,
      dishName: randomDish.name,
      confidence,
      price: randomDish.price,
      timestamp: new Date().toLocaleTimeString(),
      image: randomDish.icon,
      added: false
    };

    setCurrentResult(result);
    setRecognitionResults(prev => [result, ...prev.slice(0, 9)]); // 保留最近10条

    // 如果置信度够高且启用自动添加
    if (confidence >= recognitionConfig.confidence && autoAdd) {
      handleAddToCart(result);
    }
  }, [recognitionConfig.confidence, autoAdd, handleAddToCart]);

  // 开始/停止识别
  const toggleRecognition = () => {
    if (isRecognizing) {
      setIsRecognizing(false);
    } else {
      if (!cameraEnabled) {
        startCamera();
      }
      setIsRecognizing(true);
    }
  };

  // 识别循环
  useEffect(() => {
    let interval: any;

    if (isRecognizing && cameraEnabled && recognitionConfig.enabled) {
      interval = setInterval(simulateRecognition, recognitionConfig.interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecognizing, cameraEnabled, recognitionConfig, simulateRecognition]);

  return (
    <div className={`ai-section ${className} ${isRecognizing ? 'auto-mode' : ''}`}>
      {/* AI识别标题 */}
      <div className="ai-header">🤖 AI智能扫菜识别</div>

      {/* AI状态栏 */}
      <div className="ai-status-bar">
        <div className="status-indicator">
          <div className={`status-dot ${isRecognizing ? 'active' : ''}`}></div>
          <span id="ai-status">识别状态：{isRecognizing ? '连续识别中' : '已暂停'}</span>
        </div>
        <div className="status-indicator">
          <span>🌡️ 光线：良好</span>
        </div>
      </div>

      {/* 摄像头预览区 */}
      <div className={`camera-preview ${isRecognizing ? 'scanning' : ''}`} id="camera-preview">
        <div className="camera-icon">📸</div>
        <div className="camera-status" id="camera-status">
          {isRecognizing ? '🔄 正在扫描...' : '点击开始识别'}
        </div>
        <div className="camera-guide">💡 自动识别：检测到菜品将自动添加到订单</div>
      </div>

      {/* 识别历史记录 */}
      <div className="recognition-history" id="recognition-history">
        <div className="history-header">📝 识别记录</div>
        <div className="history-list" id="history-list">
          {recognitionResults.length > 0 ? (
            recognitionResults.map((result) => (
              <div key={result.id} className="history-item">
                <span className="history-icon">{result.image}</span>
                <div className="history-info">
                  <div className="history-name">{result.dishName}</div>
                  <div className="history-time">
                    {result.timestamp} {result.added ? '自动添加' : '待确认'}
                  </div>
                </div>
                <div className="history-price">¥{result.price.toFixed(2)}</div>
              </div>
            ))
          ) : (
            <div className="history-item">
              <span className="history-icon">🍗</span>
              <div className="history-info">
                <div className="history-name">宫保鸡丁</div>
                <div className="history-time">14:28 自动添加</div>
              </div>
              <div className="history-price">¥18.00</div>
            </div>
          )}
        </div>
      </div>

      {/* AI控制按钮 */}
      <div className="ai-controls">
        <button
          className={`ai-btn ${isRecognizing ? 'success' : ''}`}
          id="toggle-recognition"
          onClick={toggleRecognition}
        >
          {isRecognizing ? '⏸️ 暂停识别' : '▶️ 开始识别'}
        </button>
        <button className="ai-btn" onClick={() => setRecognitionResults([])}>
          🗑️ 清空记录
        </button>
        <button className="ai-btn" onClick={() => {/* 打开设置 */}}>
          ⚙️ 设置
        </button>
      </div>
    </div>
  );
};

export default AISection; 