const { contextBridge, ipc<PERSON>enderer } = require('electron');

/**
 * Electron API 安全桥接
 * 使用contextBridge安全地暴露API给渲染进程
 */
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),

  // 对话框
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),

  // 外部链接和文件
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  showItemInFolder: (path) => ipcRenderer.invoke('show-item-in-folder', path),

  // 窗口控制
  setAlwaysOnTop: (flag) => ipcRenderer.invoke('set-window-always-on-top', flag),
  minimizeToTray: () => ipcRenderer.invoke('minimize-to-tray'),

  // 更新检查
  checkForUpdates: () => ipcRenderer.invoke('check-for-updates'),

  // 打印功能
  printReceipt: (options) => ipcRenderer.invoke('print-receipt', options),

  // 文件操作
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),

  // 菜单事件监听
  onMenuAction: (callback) => {
    const validChannels = [
      'menu-new-order',
      'menu-print',
      'menu-export',
      'menu-search-order',
      'menu-refund',
      'menu-order-history',
      'menu-member',
      'menu-topup',
      'menu-member-management',
      'menu-start-recognition',
      'menu-stop-recognition',
      'menu-ai-settings',
      'menu-settings',
      'menu-backup',
      'menu-restore',
      'menu-help',
      'menu-shortcuts'
    ];

    validChannels.forEach(channel => {
      ipcRenderer.on(channel, (event, ...args) => {
        callback(channel, ...args);
      });
    });

    // 返回清理函数
    return () => {
      validChannels.forEach(channel => {
        ipcRenderer.removeAllListeners(channel);
      });
    };
  },

  // 移除监听器
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});

/**
 * 平台信息
 */
contextBridge.exposeInMainWorld('platform', {
  isWindows: process.platform === 'win32',
  isMacOS: process.platform === 'darwin',
  isLinux: process.platform === 'linux'
});

/**
 * 环境信息
 */
contextBridge.exposeInMainWorld('env', {
  NODE_ENV: process.env.NODE_ENV,
  isDev: process.env.NODE_ENV === 'development'
});

// 阻止窗口导航
window.addEventListener('beforeunload', (event) => {
  // 在实际应用中，这里可以检查是否有未保存的数据
  // event.preventDefault();
  // event.returnValue = '';
});

// 阻止拖拽文件到窗口
document.addEventListener('dragover', (e) => {
  e.preventDefault();
  e.stopPropagation();
});

document.addEventListener('drop', (e) => {
  e.preventDefault();
  e.stopPropagation();
});

// 阻止右键菜单（生产环境）
if (process.env.NODE_ENV === 'production') {
  document.addEventListener('contextmenu', (e) => {
    e.preventDefault();
  });
}

// 阻止特定快捷键（生产环境）
if (process.env.NODE_ENV === 'production') {
  document.addEventListener('keydown', (e) => {
    // 阻止 F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
    if (
      e.key === 'F12' ||
      (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
      (e.ctrlKey && e.key === 'U')
    ) {
      e.preventDefault();
    }
  });
}

console.log('Preload script loaded successfully'); 